package com.xcess.ocs.ratingengine.service;

import com.xcess.ocs.dto.RatedCdrDTO;
import com.xcess.ocs.entity.RatedCdr;
import com.xcess.ocs.entity.RateDetails;
import com.xcess.ocs.entity.RatePackage;
import com.xcess.ocs.entity.Type;
import com.xcess.ocs.entity.UnRatedCdr;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;

/**
 * Service responsible for integrating CDR processing with the rating engine.
 * This service acts as the bridge between Kafka CDR data and the
 * RadixTrie-based
 * rating algorithm, handling the complete flow from CDR receipt to rating
 * application.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CdrRatingIntegrationService {

    private final RateLookupService rateLookupService;
    private final AccountRateService accountRateService;
    // Common timestamp formats to try when parsing CDR timestamps
    private static final DateTimeFormatter[] TIMESTAMP_FORMATTERS = {
            DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss"), // Format: 14-05-2025 02:23:34
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"),
            DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"),
            DateTimeFormatter.ISO_LOCAL_DATE_TIME
    };

    /**
     * Process and rate a CDR from Kafka before database storage.
     * This method implements the complete flow:
     * 1. Create entity from DTO
     * 2. Derive rate package from account information
     * 3. Apply rate lookup using the RadixTrie
     * 4. Return appropriate entity based on rating success
     * 
     * @param cdrDto   The CDR data from Kafka
     * @param sourceId The source ID for this CDR
     * @return Either a RatedCdr (if successfully rated) or an UnRatedCdr (if rating
     *         failed)
     */
    public Object processAndRateCdr(RatedCdrDTO cdrDto, Long sourceId) {
        log.debug("Processing CDR for rating: calling={}, called={}, sourceId={}",
                cdrDto.getCallingNumber(), cdrDto.getCalledNumber(), sourceId);

        // Extract basic CDR information
        String callingNumber = cdrDto.getCallingNumber();
        String calledNumber = cdrDto.getCalledNumber();
        String startTime = cdrDto.getStartTime();
        String endTime = cdrDto.getEndTime();
        String incomingAccountId = cdrDto.getIncomingAccountId();
        String outgoingAccountId = cdrDto.getOutgoingAccountId();

        // Calculate duration
        BigDecimal durationMinutes = calculateDuration(startTime, endTime);

        try {
            // Step 1: Parse call timestamp for rating validation
            LocalDateTime callTimestamp = parseTimestamp(startTime);
            if (callTimestamp == null) {
                log.error("Timestamp parsing failed for CDR: calling={}, called={}, startTime='{}'",
                        callingNumber, calledNumber, startTime);

                return UnRatedCdr.createFromBasicInfo(
                        callingNumber, calledNumber, startTime, endTime,
                        incomingAccountId, outgoingAccountId, sourceId, durationMinutes,
                        "INVALID_TIMESTAMP", null, null);
            }

            // Step 2: Derive rate package from account
            RatePackage ratePackage = determineRatePackage(outgoingAccountId, callTimestamp);
            if (ratePackage == null) {
                String detailedError = String.format("No vendor rate package found for outgoing account: %s",
                        outgoingAccountId);
                log.warn(detailedError);

                return UnRatedCdr.createFromBasicInfo(
                        callingNumber, calledNumber, startTime, endTime,
                        incomingAccountId, outgoingAccountId, sourceId, durationMinutes,
                        "NO_VENDOR_RATE_PACKAGE", null, null);
            }

            Long ratePackageId = ratePackage.getRatePackageId();
            String packageName = ratePackage.getPackageName();

            // Step 3: Use the RateLookupService to find the best rate
            RateDetails bestRate = rateLookupService.findBestRate(
                    ratePackageId, // Derived rate package ID
                    callingNumber, // Source number
                    calledNumber, // Destination number
                    callTimestamp // Call timestamp
            );

            // Step 4: Create appropriate entity based on rating result
            if (bestRate != null) {
                // Successfully found a matching rate - create RatedCdr
                RatedCdr ratedCdr = new RatedCdr();
                ratedCdr.setCallingNumber(callingNumber);
                ratedCdr.setCalledNumber(calledNumber);
                ratedCdr.setStartTime(startTime);
                ratedCdr.setEndTime(endTime);
                ratedCdr.setIncomingAccountId(incomingAccountId);
                ratedCdr.setOutgoingAccountId(outgoingAccountId);
                ratedCdr.setSourceId(sourceId);
                ratedCdr.setDurationMinutes(durationMinutes);

                // Determine if this was a source-destination match
                boolean isSourceDestMatch = bestRate.getSourcePrefix() != null &&
                        !bestRate.getSourcePrefix().trim().isEmpty();

                // Apply rating information
                ratedCdr.applyRating(
                        BigDecimal.valueOf(bestRate.getRate()), // Applied rate
                        ratePackageId, // Rate package ID
                        packageName, // Rate package name
                        bestRate.getRateDetailsId(), // Rate detail ID
                        bestRate.getSourcePrefix(), // Matched source prefix
                        bestRate.getDestinationPrefix(), // Matched destination prefix
                        isSourceDestMatch // Phase 1 vs Phase 2 match
                );

                log.info("Successfully rated CDR: calling={}, called={}, rate={}, package={}",
                        callingNumber, calledNumber, bestRate.getRate(), packageName);

                return ratedCdr;
            } else {
                // No matching rate found - create UnRatedCdr
                log.warn("No rate found for CDR: calling={}, called={}, duration={} minutes",
                        callingNumber, calledNumber, durationMinutes);

                return UnRatedCdr.createFromBasicInfo(
                        callingNumber, calledNumber, startTime, endTime,
                        incomingAccountId, outgoingAccountId, sourceId, durationMinutes,
                        "NO_MATCHING_RATE", ratePackageId, packageName);
            }
        } catch (Exception e) {
            // Handle any errors during rating
            log.error("Failed to rate CDR: calling={}, called={}, error={}",
                    callingNumber, calledNumber, e.getMessage(), e);

            return UnRatedCdr.createFromBasicInfo(
                    callingNumber, calledNumber, startTime, endTime,
                    incomingAccountId, outgoingAccountId, sourceId, durationMinutes,
                    "RATING_ERROR: " + e.getMessage(), null, null);
        }
    }

    /**
     * Determine the appropriate rate package for this CDR based on account
     * information.
     * Implements the account-based rate package derivation logic, with fallback
     * strategies.
     * 
     * @param outgoingAccountId The outgoing account ID
     * @param callTime          The timestamp of the call
     * @return The appropriate RatePackage, or null if none found
     */
    private RatePackage determineRatePackage(String outgoingAccountId, LocalDateTime callTime) {
        // Check only the outgoing account ID when it's a vendor
        if (outgoingAccountId != null) {
            RatePackage ratePackage = accountRateService.findRatePackageForAccount(
                    outgoingAccountId, callTime);
            if (ratePackage != null) {
                // Check if the outgoing account is a vendor
                String accountType = accountRateService.getAccountType(outgoingAccountId);
                if ("VENDOR".equals(accountType)) {
                    // Only apply rates if package type is BUYING for vendors
                    if (ratePackage.getType() == Type.BUYING) {
                        log.debug("Using BUYING rate package {} from outgoing vendor account {}",
                                ratePackage.getRatePackageId(), outgoingAccountId);
                        return ratePackage;
                    } else {
                        log.debug("Ignoring SELLING rate package for vendor account {} as per business rules",
                                outgoingAccountId);
                        return null;
                    }
                }
            }
        }

        // If no vendor outgoing account with BUYING rate package found, log a warning
        log.warn("No vendor rate package with BUYING type found for outgoing account: {}",
                outgoingAccountId);
        return null;
    }

    /**
     * Calculate call duration from start and end times.
     * 
     * @param startTimeStr Start time string
     * @param endTimeStr   End time string
     * @return Duration in minutes, or null if calculation fails
     */
    private BigDecimal calculateDuration(String startTimeStr, String endTimeStr) {
        try {
            LocalDateTime startTime = parseTimestamp(startTimeStr);
            LocalDateTime endTime = parseTimestamp(endTimeStr);

            if (startTime != null && endTime != null) {
                long durationSeconds = ChronoUnit.SECONDS.between(startTime, endTime);
                return BigDecimal.valueOf(durationSeconds)
                        .divide(BigDecimal.valueOf(60), 2, BigDecimal.ROUND_HALF_UP);
            }
        } catch (Exception e) {
            log.error("Error calculating duration: error={}", e.getMessage());
        }
        return null;
    }

    /**
     * Parse timestamp string using multiple common formats.
     * This method tries several common timestamp formats to handle various input
     * formats.
     * 
     * @param timestampStr The timestamp string to parse
     * @return Parsed LocalDateTime, or null if parsing fails
     */
    private LocalDateTime parseTimestamp(String timestampStr) {
        if (timestampStr == null || timestampStr.trim().isEmpty()) {
            return null;
        }

        String cleanTimestamp = timestampStr.trim();

        for (DateTimeFormatter formatter : TIMESTAMP_FORMATTERS) {
            try {
                return LocalDateTime.parse(cleanTimestamp, formatter);
            } catch (DateTimeParseException e) {
                // Try next formatter
            }
        }

        log.warn("Unable to parse timestamp: {}", timestampStr);
        return null;
    }
}