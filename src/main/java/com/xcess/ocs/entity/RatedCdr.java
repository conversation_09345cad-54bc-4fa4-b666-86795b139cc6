package com.xcess.ocs.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "rated_cdr")
@SQLDelete(sql = "UPDATE rated_cdr SET is_deleted = true WHERE rated_cdr_id = ?")
@Where(clause = "is_deleted = false")
public class RatedCdr extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long ratedCdrId;

    @Column(name = "calling_number", nullable = false)
    private String callingNumber;

    @Column(name = "called_number", nullable = false)
    private String calledNumber;

    @Column(name = "start_time", nullable = false)
    private String startTime;

    @Column(name = "end_time", nullable = false)
    private String endTime;

    @Column(name = "incoming_account_id", nullable = false)
    private String incomingAccountId;

    @Column(name = "outgoing_account_id", nullable = false)
    private String outgoingAccountId;

    @Column(name = "source_id", nullable = false)
    private Long sourceId;

    /**
     * The applied rate per unit (e.g., per minute)
     */
    @Column(name = "applied_rate", precision = 10, scale = 4)
    private BigDecimal appliedRate;

    /**
     * The ID of the rate package used for rating
     */
    @Column(name = "rate_package_id")
    private Long ratePackageId;

    /**
     * The name of the rate package used for rating
     */
    @Column(name = "rate_package_name")
    private String ratePackageName;

    /**
     * The source prefix that was matched during rating
     */
    @Column(name = "matched_source_prefix")
    private String matchedSourcePrefix;

    /**
     * The destination prefix that was matched during rating
     */
    @Column(name = "matched_destination_prefix")
    private String matchedDestinationPrefix;

    /**
     * Timestamp when rating was applied
     */
    @Column(name = "rated_at")
    private LocalDateTime ratedAt;

    /**
     * The ID of the specific rate detail used
     */
    @Column(name = "rate_detail_id")
    private Long rateDetailId;

    /**
     * Whether this was a source-destination match (Phase 1) or destination-only
     * match (Phase 2)
     */
    @Column(name = "is_source_destination_match")
    private Boolean isSourceDestinationMatch;

    /**
     * Total calculated cost for this call (rate * duration)
     */
    @Column(name = "total_cost", precision = 10, scale = 4)
    private BigDecimal totalCost;

    /**
     * Call duration in minutes (calculated from start and end time)
     */
    @Column(name = "duration_minutes", precision = 10, scale = 2)
    private BigDecimal durationMinutes;

    /**
     * Apply rating information to this CDR
     */
    public void applyRating(BigDecimal rate, Long ratePackageId, String ratePackageName,
            Long rateDetailId, String sourcePrefix, String destinationPrefix,
            boolean isSourceDestMatch) {
        this.appliedRate = rate;
        this.ratePackageId = ratePackageId;
        this.ratePackageName = ratePackageName;
        this.rateDetailId = rateDetailId;
        this.matchedSourcePrefix = sourcePrefix;
        this.matchedDestinationPrefix = destinationPrefix;
        this.isSourceDestinationMatch = isSourceDestMatch;
        this.ratedAt = LocalDateTime.now();

        // Calculate total cost if duration is available
        if (this.durationMinutes != null && rate != null) {
            this.totalCost = rate.multiply(this.durationMinutes);
        }
    }
}