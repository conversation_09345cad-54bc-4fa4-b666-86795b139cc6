package com.xcess.ocs.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "un_rated_cdr")
@SQLDelete(sql = "UPDATE un_rated_cdr SET is_deleted = true WHERE un_rated_cdr_id = ?")
@Where(clause = "is_deleted = false")
public class UnRatedCdr extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "un_rated_cdr_id")
    private Long unRatedCdrId;

    @Column(name = "calling_number", nullable = false)
    private String callingNumber;

    @Column(name = "called_number", nullable = false)
    private String calledNumber;

    @Column(name = "start_time", nullable = false)
    private String startTime;

    @Column(name = "end_time", nullable = false)
    private String endTime;

    @Column(name = "incoming_account_id", nullable = false)
    private String incomingAccountId;

    @Column(name = "outgoing_account_id", nullable = false)
    private String outgoingAccountId;

    @Column(name = "source_id", nullable = false)
    private Long sourceId;

    /**
     * The ID of the rate package that was attempted for rating
     */
    @Column(name = "attempted_rate_package_id")
    private Long attemptedRatePackageId;

    /**
     * The name of the rate package that was attempted for rating
     */
    @Column(name = "attempted_rate_package_name")
    private String attemptedRatePackageName;

    /**
     * Reason why rating failed
     */
    @Column(name = "failure_reason")
    private String failureReason;

    /**
     * Timestamp when processing occurred
     */
    @Column(name = "processed_at")
    private LocalDateTime processedAt;

    /**
     * Call duration in minutes (calculated from start and end time)
     */
    @Column(name = "duration_minutes", precision = 10, scale = 2)
    private BigDecimal durationMinutes;

    /**
     * Create an UnRatedCdr from basic CDR information
     */
    public static UnRatedCdr createFromBasicInfo(
            String callingNumber, String calledNumber,
            String startTime, String endTime,
            String incomingAccountId, String outgoingAccountId,
            Long sourceId, BigDecimal durationMinutes,
            String failureReason, Long attemptedPackageId, String attemptedPackageName) {

        UnRatedCdr unRatedCdr = new UnRatedCdr();
        unRatedCdr.setCallingNumber(callingNumber);
        unRatedCdr.setCalledNumber(calledNumber);
        unRatedCdr.setStartTime(startTime);
        unRatedCdr.setEndTime(endTime);
        unRatedCdr.setIncomingAccountId(incomingAccountId);
        unRatedCdr.setOutgoingAccountId(outgoingAccountId);
        unRatedCdr.setSourceId(sourceId);
        unRatedCdr.setDurationMinutes(durationMinutes);
        unRatedCdr.setFailureReason(failureReason);
        unRatedCdr.setAttemptedRatePackageId(attemptedPackageId);
        unRatedCdr.setAttemptedRatePackageName(attemptedPackageName);
        unRatedCdr.setProcessedAt(LocalDateTime.now());

        return unRatedCdr;
    }
}
