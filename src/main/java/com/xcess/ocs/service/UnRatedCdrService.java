package com.xcess.ocs.service;

import com.xcess.ocs.entity.UnRatedCdr;
import com.xcess.ocs.exception.ResourceNotFoundException;
import com.xcess.ocs.repository.UnRatedCdrRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class UnRatedCdrService {

    private static final Logger logger = LoggerFactory.getLogger(UnRatedCdrService.class);

    private final UnRatedCdrRepository unRatedCdrRepository;

    public UnRatedCdrService(UnRatedCdrRepository unRatedCdrRepository) {
        this.unRatedCdrRepository = unRatedCdrRepository;
    }

    /**
     * Save an UnRatedCdr entity
     * 
     * @param unRatedCdr The UnRatedCdr entity to save
     * @return The saved UnRatedCdr entity
     * @throws IllegalArgumentException if the unRatedCdr is null
     */
    public UnRatedCdr saveUnRatedCdr(UnRatedCdr unRatedCdr) {
        if (unRatedCdr == null) {
            logger.error("Cannot save null UnRatedCdr");
            throw new IllegalArgumentException("UnRatedCdr object cannot be null");
        }

        UnRatedCdr savedEntity = unRatedCdrRepository.save(unRatedCdr);

        logger.info("Saved UnRatedCdr entity: id={}, calling={}, called={}, " +
                "reason={}, attemptedRatePackage={}",
                savedEntity.getUnRatedCdrId(),
                savedEntity.getCallingNumber(),
                savedEntity.getCalledNumber(),
                savedEntity.getFailureReason(),
                savedEntity.getAttemptedRatePackageName());

        return savedEntity;
    }

    /**
     * Find an UnRatedCdr by its ID
     * 
     * @param id The ID of the UnRatedCdr to find
     * @return The found UnRatedCdr
     * @throws ResourceNotFoundException if the UnRatedCdr is not found
     */
    @Transactional(readOnly = true)
    public UnRatedCdr findById(Long id) {
        if (id == null) {
            logger.error("Cannot find UnRatedCdr with null ID");
            throw new IllegalArgumentException("UnRatedCdr ID cannot be null");
        }

        return unRatedCdrRepository.findById(id)
                .orElseThrow(() -> {
                    logger.error("UnRatedCdr not found with id: {}", id);
                    return new ResourceNotFoundException("UnRatedCdr not found with id: " + id);
                });
    }
}
