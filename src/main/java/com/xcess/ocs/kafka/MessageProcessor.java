package com.xcess.ocs.kafka;

import com.xcess.ocs.cache.SourceCdrConfigurationCache;
import com.xcess.ocs.cache.SourceConfigurationCache;
import com.xcess.ocs.dto.RatedCdrDTO;
import com.xcess.ocs.dto.SourceCdrConfigurationDTO;
import com.xcess.ocs.dto.SourceConfigurationDTO;
import com.xcess.ocs.entity.RatedCdr;
import com.xcess.ocs.entity.UnRatedCdr;
import com.xcess.ocs.ratingengine.service.CdrRatingIntegrationService;
import com.xcess.ocs.service.RatedCdrService;
import com.xcess.ocs.service.UnRatedCdrService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Service responsible for processing CDR (Call Detail Record) messages from
 * Kafka topics.
 * This class handles the business logic of:
 * 1. Parsing raw messages from Kafka
 * 2. Mapping fields based on configured sequences
 * 3. Converting raw data to structured DTO objects
 * 4. RATING ENGINE INTEGRATION: Applying rates using RadixTrie algorithm
 * 5. Persisting enhanced rated records to the database
 *
 * FLOW: Kafka → Parse → Rate Lookup → Rate Application → Enhanced Database
 * Storage
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MessageProcessor {

    // Service for persisting rated CDRs to the database
    private final RatedCdrService ratedCdrService;

    // Service for persisting unrated CDRs to the database
    private final UnRatedCdrService unRatedCdrService;

    // Rating integration service that bridges CDR processing and rating engine
    private final CdrRatingIntegrationService cdrRatingIntegrationService;

    // Cache for accessing source configurations without database queries
    private final SourceConfigurationCache sourceConfigCache;

    // Cache for accessing CDR field configurations without database queries
    private final SourceCdrConfigurationCache sourceCdrConfigCache;

    /**
     * Processes a single message from a Kafka topic.
     * This is the main entry point called by the Kafka consumer for each received
     * message.
     * 
     * @param topicName The name of the topic the message came from
     * @param message   The message content (typically a comma-separated string)
     */
    public void processMessage(String topicName, String message) {
        try {
            log.info("Received message for topic: {}", topicName);
            log.debug("Raw message: {}", message);

            // Step 1: Get source configuration by topic name
            // This should always be valid since we only subscribe to enabled topics
            SourceConfigurationDTO sourceConfig = sourceConfigCache.getConfigurationByTopicName(topicName);
            if (sourceConfig == null) {
                log.error(
                        "Source configuration not found for topic: {}. This should not happen as we only subscribe to enabled topics.",
                        topicName);
                return;
            }

            Long sourceId = sourceConfig.getSourceId();
            log.debug("Processing message for topic: {} with sourceId: {}", topicName, sourceId);

            // Step 2: Fetch field mappings from cache
            List<SourceCdrConfigurationDTO> fieldConfigs = sourceCdrConfigCache.getConfigurationsBySourceId(sourceId);
            if (fieldConfigs.isEmpty()) {
                log.error("No field configuration found for source ID: {}", sourceId);
                return;
            }

            // Step 3: Validate field configurations for duplicate sequences
            // This is important to ensure correct parsing
            Map<Integer, Long> sequenceCounts = fieldConfigs.stream()
                    .collect(Collectors.groupingBy(SourceCdrConfigurationDTO::getSequence, Collectors.counting()));

            Set<Integer> duplicateSequences = sequenceCounts.entrySet().stream()
                    .filter(entry -> entry.getValue() > 1)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toSet());

            if (!duplicateSequences.isEmpty()) {
                log.warn("Duplicate sequences found for sourceId {}: {}", sourceId, duplicateSequences);
            }

            // Step 4: Sort fields by sequence number to determine the correct order
            List<String> fieldOrder = fieldConfigs.stream()
                    .sorted(Comparator.comparing(SourceCdrConfigurationDTO::getSequence))
                    .map(SourceCdrConfigurationDTO::getFieldName)
                    .toList();
            log.debug("Field order for topic {}: {}", topicName, fieldOrder);

            // Step 5: Parse the message into a structured DTO
            RatedCdrDTO cdrRecord = parseMessage(message, fieldOrder);
            log.info("Parsed message for topic [{}] and sourceId [{}]", topicName, sourceId);

            // Step 6: Apply rating using RadixTrie algorithm
            Object processedCdr = cdrRatingIntegrationService.processAndRateCdr(cdrRecord, sourceId);

            // Step 7: Save the record to the appropriate database table based on type
            if (processedCdr instanceof RatedCdr) {
                RatedCdr ratedCdr = (RatedCdr) processedCdr;
                ratedCdrService.saveRatedCdr(ratedCdr);
                log.info("Saved RatedCdr for topic [{}] with rate: {}",
                        topicName, ratedCdr.getAppliedRate());
            } else if (processedCdr instanceof UnRatedCdr) {
                UnRatedCdr unRatedCdr = (UnRatedCdr) processedCdr;
                unRatedCdrService.saveUnRatedCdr(unRatedCdr);
                log.info("Saved UnRatedCdr for topic [{}] with reason: {}",
                        topicName, unRatedCdr.getFailureReason());
            } else {
                log.error("Unknown CDR type returned from rating integration service: {}",
                        processedCdr != null ? processedCdr.getClass().getName() : "null");
            }
        } catch (Exception e) {
            // Comprehensive error handling with detailed logging
            log.error("Failed to process message for topic [{}]: {}", topicName, e.getMessage(), e);
        }
    }

    /**
     * Parses a comma-separated message into a RatedCdrDTO object based on the field
     * order.
     * This method maps each field in the raw message to the appropriate property in
     * the DTO
     * according to the configured field name.
     *
     * @param message    The comma-separated message to parse
     * @param fieldOrder The ordered list of field names based on their sequence
     *                   numbers
     * @return A populated RatedCdrDTO object with values extracted from the message
     * @throws IllegalArgumentException if the message format doesn't match the
     *                                  expected configuration
     */
    private RatedCdrDTO parseMessage(String message, List<String> fieldOrder) {
        // Split the message into individual fields
        String[] fields = message.split(",");

        // Validate that the number of fields matches the configuration
        if (fields.length != fieldOrder.size()) {
            throw new IllegalArgumentException("Message field count does not match configuration: " +
                    "expected " + fieldOrder.size() + " fields but got " + fields.length);
        }

        // Create a new DTO to populate
        RatedCdrDTO cdrRecord = new RatedCdrDTO();

        // Map each field value to the appropriate property based on field name
        for (int i = 0; i < fieldOrder.size(); i++) {
            String value = fields[i].trim();
            // Use switch expression (Java 14+) for cleaner mapping
            switch (fieldOrder.get(i).toUpperCase()) {
                case "CALLING_NUMBER" -> cdrRecord.setCallingNumber(value);
                case "CALLED_NUMBER" -> cdrRecord.setCalledNumber(value);
                case "START_TIME" -> cdrRecord.setStartTime(value);
                case "END_TIME" -> cdrRecord.setEndTime(value);
                case "INCOMING_ACCOUNT_ID" -> cdrRecord.setIncomingAccountId(value);
                case "OUTGOING_ACCOUNT_ID" -> cdrRecord.setOutgoingAccountId(value);
            }
        }
        return cdrRecord;
    }
}