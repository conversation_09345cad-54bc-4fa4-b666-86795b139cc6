<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                            http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.10.xsd">

    <changeSet id="remove_rating_status_columns" author="system">
        <preConditions onFail="MARK_RAN" onError="CONTINUE">
            <columnExists tableName="rated_cdr" columnName="rating_status"/>
        </preConditions>

        <dropColumn tableName="rated_cdr" columnName="rating_status"/>
        <dropColumn tableName="rated_cdr" columnName="rating_failure_reason"/>
    </changeSet>
</databaseChangeLog> 