<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                            http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.10.xsd">

    <changeSet id="create_unrated_cdr_table" author="system">
        <preConditions onFail="MARK_RAN" onError="CONTINUE">
            <not>
                <tableExists tableName="un_rated_cdr"/>
            </not>
        </preConditions>

        <createTable tableName="un_rated_cdr">
            <column name="un_rated_cdr_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>

            <!-- Core CDR fields -->
            <column name="calling_number" type="VARCHAR(50)" />
            <column name="called_number" type="VARCHAR(50)" />
            <column name="start_time" type="VARCHAR(50)" />
            <column name="end_time" type="VARCHAR(50)" />
            <column name="incoming_account_id" type="VARCHAR(50)" />
            <column name="outgoing_account_id" type="VARCHAR(50)" />
            <column name="source_id" type="BIGINT" />
            <column name="duration_minutes" type="DECIMAL(10,2)" />
            
            <!-- Unrated-specific fields -->
            <column name="attempted_rate_package_id" type="BIGINT" />
            <column name="attempted_rate_package_name" type="VARCHAR(255)" />
            <column name="failure_reason" type="VARCHAR(255)" />
            <column name="processed_at" type="TIMESTAMP" />
            
            <!-- Audit fields -->
            <column name="created_date" type="TIMESTAMP" />
            <column name="modified_date" type="TIMESTAMP" />
            <column name="is_deleted" type="BOOLEAN" defaultValueBoolean="false" />
        </createTable>
        
        <createIndex tableName="un_rated_cdr" indexName="idx_un_rated_cdr_calling">
            <column name="calling_number" />
        </createIndex>
        
        <createIndex tableName="un_rated_cdr" indexName="idx_un_rated_cdr_called">
            <column name="called_number" />
        </createIndex>
        
        <createIndex tableName="un_rated_cdr" indexName="idx_un_rated_cdr_outgoing_account">
            <column name="outgoing_account_id" />
        </createIndex>
    </changeSet>
</databaseChangeLog> 